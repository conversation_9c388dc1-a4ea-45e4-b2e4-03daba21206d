const Details = {

	init: () => {
		
		document.addEventListener('click', e => {
			// Handle input focus for regular details summary clicks
			if(e.target.matches('details summary')){
				if(!!e.target.parentNode.querySelector('input')){
					setTimeout(()=>{
						if(e.target.parentNode.open)
						e.target.parentNode.querySelector('input').focus()
					},10)
				}
			}

			// Check if this is an animated accordion that needs special handling
			const isAnimated = (e.target.matches('details[data-animate="true"] summary') || e.target.closest('details[data-animate="true"] summary'));
			let animatedDetails = null;
			let animatedContent = null;

			if(isAnimated) {
				animatedDetails = e.target.closest('details[data-animate="true"]');
				animatedContent = animatedDetails?.querySelector('.x-accordion-panel');

				if(animatedContent) {
					// Prevent default to handle our own animation
					e.preventDefault();
				}
			}

			// Store the clicked accordion for scroll position management
			let clickedAccordion = null;
			if(e.target.closest('details[type="accordion"]')) {
				clickedAccordion = e.target.closest('details[type="accordion"]');
			}

			document.querySelectorAll('details[type]').forEach( details => {
				details.type = details.getAttribute('type')
				details.group = details.getAttribute('group')

				if( details.type=='popover' && !details.contains(e.target) ){
					details.removeAttribute('open')
				}

				if( details.type=='context-menu' && !details.querySelector('summary').contains(e.target) ){
					details.removeAttribute('open')
				}

				if( details.type=='modal' && !details.contains(e.target) ){
					details.removeAttribute('open')
				}

				if( details.type=='accordion' && details.contains(e.target) ){
					// Store clicked accordion position before closing other accordions
					const clickedAccordionTop = clickedAccordion ? clickedAccordion.getBoundingClientRect().top + window.pageYOffset : 0;

					Array.from(document.querySelectorAll(`details[type="accordion"][group="${details.getAttribute('group')}"]`)).filter(oe=>oe != details).forEach(oe=>{
						oe.removeAttribute('open')
					})

					// After closing other accordions, adjust scroll position to keep clicked accordion in view
					if(clickedAccordion && !clickedAccordion.open) {
						// Use requestAnimationFrame to ensure DOM has updated
						requestAnimationFrame(() => {
							const newClickedAccordionTop = clickedAccordion.getBoundingClientRect().top + window.pageYOffset;
							const scrollDifference = newClickedAccordionTop - clickedAccordionTop;

							// Only adjust if there's a significant change and we're not already at the top
							if(Math.abs(scrollDifference) > 10 && window.pageYOffset > 0) {
								window.scrollTo({
									top: window.pageYOffset + scrollDifference,
									behavior: 'smooth'
								});
							}
						});
					}
				}

				if( details.type=='tabs' && details.contains(e.target) ){

					const group = Array.from(document.querySelectorAll(`details[type="tabs"][group="${details.group}"]`))

					group.filter(oe=>oe != details).forEach(oe=>{
						oe.removeAttribute('open')
					})
				}

				// Handle animation for this specific details element if it's animated
				if(details === animatedDetails && animatedContent && details.contains(e.target)) {
					if(details.open) {
						// Closing animation
						const startHeight = animatedContent.scrollHeight;

						// Create wrapper div for smooth animation
						const wrapper = document.createElement('div');
						wrapper.style.height = startHeight + 'px';
						wrapper.style.overflow = 'hidden';
						wrapper.style.transition = 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

						// Move content into wrapper
						animatedContent.parentNode.insertBefore(wrapper, animatedContent);
						wrapper.appendChild(animatedContent);

						requestAnimationFrame(() => {
							wrapper.style.height = '0px';
						});

						setTimeout(() => {
							// Remove open attribute and clean up
							details.removeAttribute('open');
							wrapper.parentNode.insertBefore(animatedContent, wrapper);
							wrapper.remove();
							// Dispatch toggle event after animation
							Util.events.dispatch('Details:toggle', details);
						}, 300);

					} else {
						// Opening animation - store position for scroll management
						const accordionTopBeforeOpen = details.getBoundingClientRect().top + window.pageYOffset;

						details.setAttribute('open', '');

						// Get natural height
						const endHeight = animatedContent.scrollHeight;

						// Create wrapper div
						const wrapper = document.createElement('div');
						wrapper.style.height = '0px';
						wrapper.style.overflow = 'hidden';
						wrapper.style.transition = 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

						// Move content into wrapper
						animatedContent.parentNode.insertBefore(wrapper, animatedContent);
						wrapper.appendChild(animatedContent);

						requestAnimationFrame(() => {
							wrapper.style.height = endHeight + 'px';
						});

						setTimeout(() => {
							// Move content back and remove wrapper
							wrapper.parentNode.insertBefore(animatedContent, wrapper);
							wrapper.remove();

							// Ensure the accordion stays in view after animation
							const accordionTopAfterOpen = details.getBoundingClientRect().top + window.pageYOffset;
							if(accordionTopAfterOpen !== accordionTopBeforeOpen) {
								window.scrollTo({
									top: accordionTopBeforeOpen,
									behavior: 'smooth'
								});
							}

							// Dispatch toggle event after animation
							Util.events.dispatch('Details:toggle', details);
						}, 300);
					}
				} else if(details.contains(e.target)) {
					// Regular toggle event for non-animated details
					Util.events.dispatch('Details:toggle', details)
				}

			})

		})

		document.addEventListener('keyup', e => {
			if(!!e.code && e.code.toUpperCase=='ESCAPE') {
				// document.querySelectorAll('details[type]')
			}
		})

	},
	close: group => {
		document.querySelectorAll(`details[group="${group}"]`).forEach(el=>{
			el.open=false
			Util.events.dispatch('Details:toggle', el)
		})

	}
}

window.Details = Details;
// export default Details

Details.init()